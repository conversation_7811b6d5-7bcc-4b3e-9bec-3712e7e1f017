import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/attachment_model.dart';
import '../screens/attachment_viewer_screen.dart';
import '../screens/video_player_screen.dart';

/// Widget for displaying and interacting with attachments
class AttachmentViewerWidget extends StatelessWidget {
  final List<AttachmentModel> attachments;
  final bool showActions;
  final Function(AttachmentModel)? onDelete;
  final Function(AttachmentModel)? onDownload;

  const AttachmentViewerWidget({
    super.key,
    required this.attachments,
    this.showActions = false,
    this.onDelete,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المرفقات (${attachments.length})',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildAttachmentGrid(context),
      ],
    );
  }

  Widget _buildAttachmentGrid(BuildContext context) {
    // Group attachments by type
    final images = attachments.where((a) => a.isImage).toList();
    final videos = attachments.where((a) => a.isVideo).toList();
    final documents = attachments.where((a) => a.isDocument).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images
        if (images.isNotEmpty) ...[
          _buildSectionHeader(context, 'الصور', Icons.image, images.length),
          const SizedBox(height: 8),
          _buildImageGrid(context, images),
          const SizedBox(height: 16),
        ],

        // Videos
        if (videos.isNotEmpty) ...[
          _buildSectionHeader(context, 'الفيديوهات', Icons.videocam, videos.length),
          const SizedBox(height: 8),
          _buildVideoList(context, videos),
          const SizedBox(height: 16),
        ],

        // Documents
        if (documents.isNotEmpty) ...[
          _buildSectionHeader(context, 'المستندات', Icons.description, documents.length),
          const SizedBox(height: 8),
          _buildDocumentList(context, documents),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon, int count) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          '$title ($count)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildImageGrid(BuildContext context, List<AttachmentModel> images) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return _buildImageTile(context, image);
      },
    );
  }

  Widget _buildImageTile(BuildContext context, AttachmentModel image) {
    return GestureDetector(
      onTap: () => _showImageViewer(context, image),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: image.directDownloadUrl,
                fit: BoxFit.cover,
                httpHeaders: const {
                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                  'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                  'Accept-Language': 'en-US,en;q=0.9',
                  'Accept-Encoding': 'gzip, deflate, br',
                  'Connection': 'keep-alive',
                  'Upgrade-Insecure-Requests': '1',
                },
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade200,
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade200,
                  child: const Icon(Icons.error, color: Colors.red),
                ),
              ),
              if (showActions)
                Positioned(
                  top: 4,
                  right: 4,
                  child: _buildActionMenu(context, image),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoList(BuildContext context, List<AttachmentModel> videos) {
    return Column(
      children: videos.map((video) => _buildVideoTile(context, video)).toList(),
    );
  }

  Widget _buildVideoTile(BuildContext context, AttachmentModel video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
            child: video.thumbnailUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: video.thumbnailUrl!,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => const Icon(Icons.videocam),
                    ),
                  )
                : const Icon(Icons.videocam, size: 30),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  video.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  video.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _playVideo(context, video),
            icon: const Icon(Icons.play_circle_outline),
            tooltip: 'تشغيل الفيديو',
          ),
          if (showActions) _buildActionMenu(context, video),
        ],
      ),
    );
  }

  Widget _buildDocumentList(BuildContext context, List<AttachmentModel> documents) {
    return Column(
      children: documents.map((doc) => _buildDocumentTile(context, doc)).toList(),
    );
  }

  Widget _buildDocumentTile(BuildContext context, AttachmentModel document) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getDocumentColor(document.fileExtension),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                document.fileExtension.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  document.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _openDocument(context, document),
            icon: const Icon(Icons.open_in_new),
            tooltip: 'فتح المستند',
          ),
          if (showActions) _buildActionMenu(context, document),
        ],
      ),
    );
  }

  Widget _buildActionMenu(BuildContext context, AttachmentModel attachment) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, size: 20),
      onSelected: (value) {
        switch (value) {
          case 'download':
            onDownload?.call(attachment);
            break;
          case 'delete':
            onDelete?.call(attachment);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'download',
          child: Row(
            children: [
              Icon(Icons.download, size: 18),
              SizedBox(width: 8),
              Text('تحميل'),
            ],
          ),
        ),
        if (showActions)
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 18, color: Colors.red),
                SizedBox(width: 8),
                Text('حذف', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  void _showImageViewer(BuildContext context, AttachmentModel image) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AttachmentViewerScreen(
          attachment: image,
          allAttachments: attachments.where((a) => a.isImage).toList(),
          initialIndex: attachments.where((a) => a.isImage).toList().indexOf(image),
        ),
      ),
    );
  }

  void _playVideo(BuildContext context, AttachmentModel video) {
    // تشغيل الفيديو داخل التطبيق
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(attachment: video),
      ),
    );
  }

  void _openDocument(BuildContext context, AttachmentModel document) {
    // Open document URL in browser
    _openUrl(document.url);
  }

  void _openUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Color _getDocumentColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'txt':
        return Colors.grey;
      case 'rtf':
        return Colors.orange;
      case 'odt':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
