import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/attachment_model.dart';
import '../screens/attachment_viewer_screen.dart';
import '../screens/video_player_screen.dart';
import '../widgets/cached_attachment_widget.dart';

/// Widget for displaying and interacting with attachments
class AttachmentViewerWidget extends StatelessWidget {
  final List<AttachmentModel> attachments;
  final bool showActions;
  final Function(AttachmentModel)? onDelete;
  final Function(AttachmentModel)? onDownload;

  const AttachmentViewerWidget({
    super.key,
    required this.attachments,
    this.showActions = false,
    this.onDelete,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المرفقات (${attachments.length})',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildAttachmentGrid(context),
      ],
    );
  }

  Widget _buildAttachmentGrid(BuildContext context) {
    // Group attachments by type
    final images = attachments.where((a) => a.isImage).toList();
    final videos = attachments.where((a) => a.isVideo).toList();
    final documents = attachments.where((a) => a.isDocument).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images
        if (images.isNotEmpty) ...[
          _buildSectionHeader(context, 'الصور', Icons.image, images.length),
          const SizedBox(height: 8),
          _buildImageGrid(context, images),
          const SizedBox(height: 16),
        ],

        // Videos
        if (videos.isNotEmpty) ...[
          _buildSectionHeader(context, 'الفيديوهات', Icons.videocam, videos.length),
          const SizedBox(height: 8),
          _buildVideoList(context, videos),
          const SizedBox(height: 16),
        ],

        // Documents
        if (documents.isNotEmpty) ...[
          _buildSectionHeader(context, 'المستندات', Icons.description, documents.length),
          const SizedBox(height: 8),
          _buildDocumentList(context, documents),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon, int count) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          '$title ($count)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildImageGrid(BuildContext context, List<AttachmentModel> images) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return _buildImageTile(context, image);
      },
    );
  }

  Widget _buildImageTile(BuildContext context, AttachmentModel image) {
    return GestureDetector(
      onTap: () => _showImageViewer(context, image),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedAttachmentWidget(
                attachment: image,
                fit: BoxFit.cover,
                enableThumbnail: true,
              ),
              if (showActions)
                Positioned(
                  top: 4,
                  right: 4,
                  child: _buildActionMenu(context, image),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoList(BuildContext context, List<AttachmentModel> videos) {
    return Column(
      children: videos.map((video) => _buildVideoTile(context, video)).toList(),
    );
  }

  Widget _buildVideoTile(BuildContext context, AttachmentModel video) {
    return GestureDetector(
      onTap: () => _playVideo(context, video),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Video preview with play button overlay
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                    child: video.thumbnailUrl != null
                        ? CachedNetworkImage(
                            imageUrl: video.thumbnailUrl!,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade300,
                              child: const Icon(Icons.videocam, size: 40, color: Colors.grey),
                            ),
                          )
                        : Container(
                            color: Colors.grey.shade300,
                            child: const Icon(Icons.videocam, size: 40, color: Colors.grey),
                          ),
                  ),
                  // Play button overlay
                  Center(
                    child: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),
                  // Duration badge (if available)
                  if (video.metadata?['duration'] != null)
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _formatDuration(video.metadata!['duration']),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  // Actions menu
                  if (showActions)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: _buildActionMenu(context, video),
                    ),
                ],
              ),
            ),
            // Video info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    video.fileName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.file_present, size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        video.formattedFileSize,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const Spacer(),
                      Icon(Icons.videocam, size: 14, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        video.fileExtension.toUpperCase(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentList(BuildContext context, List<AttachmentModel> documents) {
    return Column(
      children: documents.map((doc) => _buildDocumentTile(context, doc)).toList(),
    );
  }

  Widget _buildDocumentTile(BuildContext context, AttachmentModel document) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getDocumentColor(document.fileExtension),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                document.fileExtension.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  document.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _openDocument(context, document),
            icon: const Icon(Icons.open_in_new),
            tooltip: 'فتح المستند',
          ),
          if (showActions) _buildActionMenu(context, document),
        ],
      ),
    );
  }

  Widget _buildActionMenu(BuildContext context, AttachmentModel attachment) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, size: 20),
      onSelected: (value) {
        switch (value) {
          case 'download':
            onDownload?.call(attachment);
            break;
          case 'delete':
            onDelete?.call(attachment);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'download',
          child: Row(
            children: [
              Icon(Icons.download, size: 18),
              SizedBox(width: 8),
              Text('تحميل'),
            ],
          ),
        ),
        if (showActions)
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 18, color: Colors.red),
                SizedBox(width: 8),
                Text('حذف', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
      ],
    );
  }

  void _showImageViewer(BuildContext context, AttachmentModel image) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AttachmentViewerScreen(
          attachment: image,
          allAttachments: attachments.where((a) => a.isImage).toList(),
          initialIndex: attachments.where((a) => a.isImage).toList().indexOf(image),
        ),
      ),
    );
  }

  void _playVideo(BuildContext context, AttachmentModel video) {
    // تشغيل الفيديو داخل التطبيق
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(attachment: video),
      ),
    );
  }

  void _openDocument(BuildContext context, AttachmentModel document) {
    // Open document URL in browser
    _openUrl(document.url);
  }

  void _openUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Color _getDocumentColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'txt':
        return Colors.grey;
      case 'rtf':
        return Colors.orange;
      case 'odt':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatDuration(dynamic duration) {
    if (duration == null) return '';

    int seconds = 0;
    if (duration is int) {
      seconds = duration;
    } else if (duration is double) {
      seconds = duration.round();
    } else if (duration is String) {
      seconds = int.tryParse(duration) ?? 0;
    }

    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '0:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
}
