import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/attachment_model.dart';
import '../services/enhanced_attachment_service.dart';
import '../services/cloud_upload_service.dart';

/// Widget for picking and managing attachments
class AttachmentPickerWidget extends StatefulWidget {
  final List<AttachmentModel> initialAttachments;
  final Function(List<AttachmentModel>) onAttachmentsChanged;
  final Function(bool)? onUploadStateChanged;
  final int maxAttachments;
  final bool allowImages;
  final bool allowVideos;
  final bool allowDocuments;

  const AttachmentPickerWidget({
    super.key,
    this.initialAttachments = const [],
    required this.onAttachmentsChanged,
    this.onUploadStateChanged,
    this.maxAttachments = 10,
    this.allowImages = true,
    this.allowVideos = true,
    this.allowDocuments = true,
  });

  @override
  State<AttachmentPickerWidget> createState() => _AttachmentPickerWidgetState();
}

class _AttachmentPickerWidgetState extends State<AttachmentPickerWidget> {
  List<AttachmentModel> _attachments = [];
  List<AttachmentModel> _originalAttachments = [];
  bool _isLoading = false;
  final Map<String, double> _uploadProgress = {}; // Track upload progress for each file
  final Map<String, bool> _uploadStatus = {}; // Track upload success/failure
  final Map<String, bool> _uploadCancelled = {}; // Track cancelled uploads

  @override
  void initState() {
    super.initState();
    _attachments = List.from(widget.initialAttachments);
    _originalAttachments = List.from(widget.initialAttachments);
  }

  @override
  void dispose() {
    // Only clean up if widget is being disposed without saving
    // Don't clean up if attachments were successfully saved
    if (!_attachmentsSaved) {
      _cleanupUnusedAttachments();
    }
    super.dispose();
  }

  bool _attachmentsSaved = false;

  /// Mark attachments as saved to prevent cleanup
  void markAttachmentsAsSaved() {
    _attachmentsSaved = true;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: 12),
        _buildAttachmentButtons(context),
        if (_attachments.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildAttachmentList(context),
        ],
        if (_isLoading) ...[
          const SizedBox(height: 16),
          const Center(child: CircularProgressIndicator()),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.attach_file,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'المرفقات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Text(
          '${_attachments.length}/${widget.maxAttachments}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentButtons(BuildContext context) {
    final canAddMore = _attachments.length < widget.maxAttachments;

    return Column(
      children: [
        // زر إضافة مرفق رئيسي
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: canAddMore ? () => _showAttachmentOptions(context) : null,
            icon: const Icon(Icons.attach_file),
            label: const Text('إضافة مرفق'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        if (!canAddMore)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'تم الوصول للحد الأقصى (${widget.maxAttachments} ملفات)',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  // عرض خيارات المرفقات في نافذة منبثقة
  void _showAttachmentOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان النافذة
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'اختر طريقة الرفع',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),

                // خيارات الرفع
                if (widget.allowImages) ...[
                  Row(
                    children: [
                      Expanded(
                        child: _buildOptionCard(
                          context,
                          icon: Icons.camera_alt,
                          title: 'التقاط صورة',
                          subtitle: 'من الكاميرا',
                          onTap: () {
                            Navigator.pop(context);
                            _pickImages(ImageSource.camera);
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildOptionCard(
                          context,
                          icon: Icons.photo_library,
                          title: 'تحميل صورة',
                          subtitle: 'من المعرض',
                          onTap: () {
                            Navigator.pop(context);
                            _pickImages(ImageSource.gallery);
                          },
                        ),
                      ),
                    ],
                  ),
                  if (widget.allowVideos || widget.allowDocuments)
                    const SizedBox(height: 16),
                ],

                if (widget.allowVideos || widget.allowDocuments) ...[
                  Row(
                    children: [
                      if (widget.allowVideos) ...[
                        Expanded(
                          child: _buildOptionCard(
                            context,
                            icon: Icons.videocam,
                            title: 'فيديو',
                            subtitle: 'تسجيل أو اختيار',
                            onTap: () {
                              Navigator.pop(context);
                              _pickVideos();
                            },
                          ),
                        ),
                        if (widget.allowDocuments) const SizedBox(width: 12),
                      ],
                      if (widget.allowDocuments) ...[
                        Expanded(
                          child: _buildOptionCard(
                            context,
                            icon: Icons.description,
                            title: 'مستند',
                            subtitle: 'PDF, DOC, TXT',
                            onTap: () {
                              Navigator.pop(context);
                              _pickDocuments();
                            },
                          ),
                        ),
                      ],
                    ],
                  ),
                ],

                const SizedBox(height: 20),

                // زر إلغاء
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),

                // Add safe area padding at bottom
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة خيار
  Widget _buildOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الملفات المحددة:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _attachments.length,
          itemBuilder: (context, index) {
            final attachment = _attachments[index];
            return _buildAttachmentItem(context, attachment, index);
          },
        ),
      ],
    );
  }

  Widget _buildAttachmentItem(BuildContext context, AttachmentModel attachment, int index) {
    // Use attachment ID if available, otherwise use fileName as fallback
    final trackingKey = attachment.id.isNotEmpty ? attachment.id : attachment.fileName;
    final isUploading = _uploadProgress.containsKey(trackingKey);
    final uploadProgress = _uploadProgress[trackingKey] ?? 0.0;
    final uploadSuccess = _uploadStatus[trackingKey] ?? true;
    final isCompleted = attachment.url.isNotEmpty && !isUploading;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: isCompleted
            ? Colors.green.shade300
            : isUploading
              ? (uploadSuccess ? Colors.blue.shade300 : Colors.red.shade300)
              : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isCompleted
          ? Colors.green.shade50
          : isUploading
            ? (uploadSuccess ? Colors.blue.shade50 : Colors.red.shade50)
            : null,
      ),
      child: Column(
        children: [
          Row(
            children: [
              _buildAttachmentIcon(attachment, isUploading: isUploading, uploadSuccess: uploadSuccess),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      attachment.fileName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: [
                        Text(
                          attachment.formattedFileSize,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getTypeColor(attachment.type),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getTypeLabel(attachment.type),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (isCompleted)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.withAlpha(30),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'تم الرفع',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        else if (isUploading)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: uploadSuccess
                                  ? Colors.blue.withAlpha(30)
                                  : Colors.red.withAlpha(30),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (uploadSuccess)
                                  SizedBox(
                                    width: 12,
                                    height: 12,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 1.5,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                    ),
                                  )
                                else
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 12,
                                  ),
                                const SizedBox(width: 4),
                                Text(
                                  uploadSuccess ? 'جاري الرفع' : 'فشل الرفع',
                                  style: TextStyle(
                                    color: uploadSuccess ? Colors.blue : Colors.red,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              // Show status and action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isCompleted) ...[
                    // Success indicator for completed uploads
                    Icon(Icons.check_circle, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    // Delete button for completed uploads
                    IconButton(
                      onPressed: () => _removeAttachment(index),
                      icon: const Icon(Icons.delete, color: Colors.red, size: 18),
                      tooltip: 'حذف',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                  ] else if (isUploading && uploadSuccess) ...[
                    // Progress indicator during upload
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        value: uploadProgress,
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Cancel button during upload
                    IconButton(
                      onPressed: () => _cancelUpload(attachment, index),
                      icon: const Icon(Icons.cancel, color: Colors.orange, size: 18),
                      tooltip: 'إلغاء الرفع',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                  ] else if (isUploading && !uploadSuccess) ...[
                    // Error indicator for failed uploads
                    Icon(Icons.error, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    // Remove button for failed uploads
                    IconButton(
                      onPressed: () => _removeAttachment(index),
                      icon: const Icon(Icons.close, color: Colors.red, size: 18),
                      tooltip: 'إزالة',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                  ] else ...[
                    // Remove button for files not yet uploaded
                    IconButton(
                      onPressed: () => _removeAttachment(index),
                      icon: const Icon(Icons.close, color: Colors.red, size: 18),
                      tooltip: 'إزالة',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                  ],
                ],
              ),
            ],
          ),

          // Progress bar for uploading files
          if (isUploading && uploadSuccess) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: uploadProgress,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        '${(uploadProgress * 100).toInt()}%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.blue,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'جاري الرفع للتخزين السحابي...',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => _cancelUpload(attachment, index),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.orange,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: const Text(
                    'إلغاء',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAttachmentIcon(AttachmentModel attachment, {bool isUploading = false, bool uploadSuccess = true}) {
    IconData iconData;
    Color iconColor;

    switch (attachment.type) {
      case AttachmentType.image:
        iconData = Icons.image;
        iconColor = Colors.green;
        break;
      case AttachmentType.video:
        iconData = Icons.videocam;
        iconColor = Colors.blue;
        break;
      case AttachmentType.document:
        iconData = Icons.description;
        iconColor = Colors.orange;
        break;
    }

    // Modify color based on upload status
    if (isUploading) {
      iconColor = uploadSuccess ? Colors.blue : Colors.red;
    } else if (attachment.url.isNotEmpty) {
      iconColor = Colors.green; // Completed upload
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(iconData, color: iconColor, size: 24),
          if (attachment.url.isNotEmpty && !isUploading)
            Positioned(
              bottom: 2,
              right: 2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 8,
                ),
              ),
            )
          else if (isUploading && uploadSuccess)
            Positioned(
              bottom: 2,
              right: 2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.cloud_upload,
                  color: Colors.white,
                  size: 8,
                ),
              ),
            )
          else if (isUploading && !uploadSuccess)
            Positioned(
              bottom: 2,
              right: 2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 8,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getTypeColor(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Colors.green;
      case AttachmentType.video:
        return Colors.blue;
      case AttachmentType.document:
        return Colors.orange;
    }
  }

  String _getTypeLabel(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return 'صورة';
      case AttachmentType.video:
        return 'فيديو';
      case AttachmentType.document:
        return 'مستند';
    }
  }

  Future<void> _pickImages(ImageSource source) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final attachments = await EnhancedAttachmentService.pickImages(
        source: source,
        allowMultiple: source == ImageSource.gallery,
      );

      await _addAttachmentsWithProgress(attachments);
    } catch (e) {
      String errorMessage = 'فشل في اختيار الصور';
      if (e.toString().contains('File size too large')) {
        errorMessage = 'حجم الملف كبير جداً. الحد الأقصى للصور 10MB';
      } else if (e.toString().contains('Permission denied')) {
        errorMessage = 'تم رفض الإذن للوصول للصور';
      } else {
        errorMessage = 'فشل في اختيار الصور: ${e.toString()}';
      }
      _showError(errorMessage);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickVideos() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final attachments = await EnhancedAttachmentService.pickVideos();
      await _addAttachmentsWithProgress(attachments);
    } catch (e) {
      String errorMessage = 'فشل في اختيار الفيديو';
      if (e.toString().contains('File size too large')) {
        errorMessage = 'حجم الفيديو كبير جداً. الحد الأقصى 50MB';
      } else if (e.toString().contains('Permission denied')) {
        errorMessage = 'تم رفض الإذن للوصول للفيديوهات';
      } else {
        errorMessage = 'فشل في اختيار الفيديو: ${e.toString()}';
      }
      _showError(errorMessage);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickDocuments() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final attachments = await EnhancedAttachmentService.pickDocuments();
      await _addAttachmentsWithProgress(attachments);
    } catch (e) {
      String errorMessage = 'فشل في اختيار المستندات';
      if (e.toString().contains('File size too large')) {
        errorMessage = 'حجم المستند كبير جداً. الحد الأقصى 25MB';
      } else if (e.toString().contains('Permission denied')) {
        errorMessage = 'تم رفض الإذن للوصول للملفات';
      } else {
        errorMessage = 'فشل في اختيار المستندات: ${e.toString()}';
      }
      _showError(errorMessage);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Add attachments with upload progress tracking
  Future<void> _addAttachmentsWithProgress(List<EnhancedAttachmentModel> newAttachments) async {
    final remainingSlots = widget.maxAttachments - _attachments.length;
    final attachmentsToAdd = newAttachments.take(remainingSlots).toList();

    if (attachmentsToAdd.isEmpty) {
      if (newAttachments.length > remainingSlots) {
        _showError('تم تجاهل بعض الملفات. الحد الأقصى ${widget.maxAttachments} ملفات.');
      }
      return;
    }

    // Notify parent that upload is starting
    widget.onUploadStateChanged?.call(true);

    // Add attachments with pending status first
    final pendingAttachments = <EnhancedAttachmentModel>[];
    final baseTimestamp = DateTime.now().millisecondsSinceEpoch;

    for (int i = 0; i < attachmentsToAdd.length; i++) {
      final attachment = attachmentsToAdd[i];
      // Create a unique ID that won't be empty - use timestamp + index + random component
      final tempId = 'temp_${baseTimestamp}_${i}_${attachment.fileName.hashCode.abs()}';

      // Initialize progress tracking
      _uploadProgress[tempId] = 0.0;
      _uploadStatus[tempId] = true; // Start with true to show "uploading" state

      final pendingAttachment = attachment.copyWith(
        id: tempId,
        url: '', // Will be set after successful upload
      );
      pendingAttachments.add(pendingAttachment);

      debugPrint('Created pending attachment with ID: $tempId, fileName: ${attachment.fileName}');
    }

    setState(() {
      _attachments.addAll(pendingAttachments);
    });

    // Upload each attachment
    final successfulAttachments = <AttachmentModel>[];
    final failedAttachments = <String>[];

    for (int i = 0; i < pendingAttachments.length; i++) {
      final attachment = pendingAttachments[i];
      final tempId = attachment.id;

      // Check if upload was cancelled before starting
      if (_uploadCancelled[tempId] == true) {
        debugPrint('Upload cancelled before starting for: ${attachment.fileName}');
        continue;
      }

      try {
        // Get file data from enhanced attachment
        final fileData = attachment.fileData;

        if (fileData == null || fileData.isEmpty) {
          throw Exception('File data is missing');
        }

        // Check if upload was cancelled before actual upload
        if (_uploadCancelled[tempId] == true) {
          debugPrint('Upload cancelled before cloud upload for: ${attachment.fileName}');
          continue;
        }

        // Upload to cloud storage using the real service
        final uploadedUrl = await CloudUploadService.uploadFile(
          attachment: attachment,
          fileData: fileData,
          onProgress: (progress) {
            // Check if upload was cancelled during progress
            if (_uploadCancelled[tempId] == true) {
              debugPrint('Upload cancelled during progress for: ${attachment.fileName}');
              return;
            }
            if (mounted) {
              setState(() {
                _uploadProgress[tempId] = progress;
              });
            }
          },
        );

        // Check if upload was cancelled after upload
        if (_uploadCancelled[tempId] == true) {
          debugPrint('Upload cancelled after cloud upload for: ${attachment.fileName}');
          continue;
        }

        // Validate upload result
        if (uploadedUrl.isEmpty) {
          throw Exception('Upload failed: No URL returned');
        }

        // Update attachment with actual URL from upload (keep the original ID)
        final uploadedAttachment = attachment.copyWith(
          id: tempId, // Explicitly keep the temp ID
          url: uploadedUrl,
        );

        successfulAttachments.add(uploadedAttachment);

        // Update the attachment in the list and clean up progress tracking
        final index = _attachments.indexWhere((a) => a.id == tempId);
        debugPrint('Looking for attachment with tempId: $tempId, found at index: $index');

        if (index != -1 && mounted && _uploadCancelled[tempId] != true) {
          debugPrint('Updating attachment at index $index with URL: $uploadedUrl');
          setState(() {
            _attachments[index] = uploadedAttachment;
          });

          // Clean up progress tracking for completed upload
          await Future.delayed(const Duration(milliseconds: 500));
          _uploadProgress.remove(tempId);
          _uploadStatus.remove(tempId);
          _uploadCancelled.remove(tempId);
          debugPrint('Cleaned up progress tracking for: $tempId');
        } else if (_uploadCancelled[tempId] == true) {
          debugPrint('Upload was cancelled, not updating UI for: ${attachment.fileName}');
        } else {
          debugPrint('ERROR: Could not find attachment with tempId: $tempId');
        }

        debugPrint('File uploaded successfully: ${attachment.fileName}');

      } catch (e) {
        // Check if this was a cancellation, not a real error
        if (_uploadCancelled[tempId] == true) {
          debugPrint('Upload was cancelled (not an error) for: ${attachment.fileName}');
          continue;
        }

        debugPrint('Error uploading file ${attachment.fileName}: $e');

        // Mark as failed
        if (mounted) {
          setState(() {
            _uploadStatus[tempId] = false;
          });
        }

        failedAttachments.add(attachment.fileName);

        // Remove failed attachment from list
        if (mounted) {
          setState(() {
            _attachments.removeWhere((a) => a.id == tempId);
          });
        }
      }
    }

    // Clean up any remaining progress tracking for failed uploads
    await Future.delayed(const Duration(seconds: 1));
    for (final attachment in pendingAttachments) {
      if (_uploadProgress.containsKey(attachment.id)) {
        _uploadProgress.remove(attachment.id);
        _uploadStatus.remove(attachment.id);
        _uploadCancelled.remove(attachment.id);
      }
    }

    // Notify parent widget with only successful attachments
    widget.onAttachmentsChanged(_attachments);

    // Notify parent that upload is complete
    widget.onUploadStateChanged?.call(false);

    // Show results
    if (successfulAttachments.isNotEmpty) {
      final count = successfulAttachments.length;
      _showSuccess('تم رفع $count ${count == 1 ? 'ملف' : 'ملفات'} بنجاح');
    }

    if (failedAttachments.isNotEmpty) {
      _showError('فشل في رفع: ${failedAttachments.join(', ')}');
    }

    if (newAttachments.length > remainingSlots) {
      _showError('تم تجاهل بعض الملفات. الحد الأقصى ${widget.maxAttachments} ملفات.');
    }
  }

  void _cancelUpload(AttachmentModel attachment, int index) {
    final trackingKey = attachment.id.isNotEmpty ? attachment.id : attachment.fileName;

    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الرفع'),
        content: Text('هل تريد إلغاء رفع "${attachment.fileName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performCancelUpload(attachment, index, trackingKey);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );
  }

  void _performCancelUpload(AttachmentModel attachment, int index, String trackingKey) {
    debugPrint('Cancelling upload for: ${attachment.fileName}');

    // Mark upload as cancelled to stop the upload process
    setState(() {
      _uploadCancelled[trackingKey] = true;
      _uploadStatus[trackingKey] = false;
    });

    // Remove from attachments list
    setState(() {
      _attachments.removeAt(index);
    });

    // Clean up progress tracking
    _uploadProgress.remove(trackingKey);
    _uploadStatus.remove(trackingKey);
    _uploadCancelled.remove(trackingKey);

    // Notify parent widget
    widget.onAttachmentsChanged(_attachments);

    // Show cancellation message
    _showSuccess('تم إلغاء رفع "${attachment.fileName}"');

    // Check if all uploads are complete and notify parent
    final hasActiveUploads = _uploadProgress.isNotEmpty;
    if (!hasActiveUploads) {
      widget.onUploadStateChanged?.call(false);
    }
  }

  void _removeAttachment(int index) {
    if (index < 0 || index >= _attachments.length) {
      debugPrint('Invalid attachment index: $index');
      return;
    }

    final attachment = _attachments[index];

    // Show confirmation dialog for uploaded files
    if (attachment.url.isNotEmpty) {
      _showDeleteConfirmation(context, attachment, index);
    } else {
      // Remove immediately if not uploaded yet
      _performRemoveAttachment(index);
    }
  }

  void _showDeleteConfirmation(BuildContext context, AttachmentModel attachment, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف "${attachment.fileName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performRemoveAttachment(index);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _performRemoveAttachment(int index) async {
    if (index < 0 || index >= _attachments.length) {
      debugPrint('Invalid attachment index for removal: $index');
      return;
    }

    final attachment = _attachments[index];
    debugPrint('Deleting attachment at index: $index');
    debugPrint('Attachment fileName: "${attachment.fileName}"');
    debugPrint('Attachment URL: "${attachment.url}"');
    debugPrint('Current attachments count: ${_attachments.length}');

    try {
      // Delete from cloud storage if it has a URL (was uploaded)
      if (attachment.url.isNotEmpty) {
        debugPrint('Deleting attachment from cloud storage: ${attachment.fileName}');
        await CloudUploadService.deleteFile(attachment.url);
        debugPrint('Successfully deleted from cloud storage: ${attachment.fileName}');
      }

      // Clean up progress tracking (use attachment ID if available, otherwise use fileName)
      final trackingKey = attachment.id.isNotEmpty ? attachment.id : attachment.fileName;
      _uploadProgress.remove(trackingKey);
      _uploadStatus.remove(trackingKey);
      _uploadCancelled.remove(trackingKey);

      // Remove from local list by index - this is the key fix!
      setState(() {
        _attachments.removeAt(index);
      });

      debugPrint('Updated attachments count: ${_attachments.length}');

      // Notify parent widget with updated list
      widget.onAttachmentsChanged(_attachments);

      // Show success message
      _showSuccess('تم حذف "${attachment.fileName}" بنجاح');

    } catch (e) {
      debugPrint('Error deleting attachment: $e');

      // Still remove from local list even if cloud deletion fails
      setState(() {
        _attachments.removeAt(index);
      });

      // Notify parent widget even if cloud deletion failed
      widget.onAttachmentsChanged(_attachments);

      _showError('تم حذف الملف محلياً، لكن فشل حذفه من التخزين السحابي: $e');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'موافق',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _cleanupUnusedAttachments() async {
    // Find attachments that were uploaded but not in original list
    final unusedAttachments = _attachments.where((attachment) =>
        attachment.url.isNotEmpty && !_originalAttachments.contains(attachment)).toList();

    for (final attachment in unusedAttachments) {
      try {
        await CloudUploadService.deleteFile(attachment.url);
        if (attachment.thumbnailUrl != null && attachment.thumbnailUrl!.isNotEmpty) {
          await CloudUploadService.deleteFile(attachment.thumbnailUrl!);
        }
        debugPrint('Cleaned up unused attachment: ${attachment.fileName}');
      } catch (e) {
        debugPrint('Error cleaning up attachment: $e');
      }
    }
  }
}
