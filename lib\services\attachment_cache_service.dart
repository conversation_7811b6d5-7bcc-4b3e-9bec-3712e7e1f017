import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/attachment_model.dart';
import 'asset_optimization_service.dart';
import 'video_thumbnail_service.dart';

/// خدمة متقدمة لإدارة cache المرفقات
class AttachmentCacheService {
  static AttachmentCacheService? _instance;
  static AttachmentCacheService get instance => _instance ??= AttachmentCacheService._();

  AttachmentCacheService._();

  static const String _cacheDirectoryName = 'attachment_cache';
  static const String _thumbnailDirectoryName = 'thumbnail_cache';
  static const String _metadataKey = 'attachment_cache_metadata';
  static const int _maxCacheSize = 200 * 1024 * 1024; // Reduced to 200 MB
  static const int _maxCacheAge = 14; // Reduced to 14 days
  static const int _maxThumbnailSize = 50 * 1024 * 1024; // 50 MB for thumbnails
  static const int _thumbnailQuality = 60; // Lower quality for thumbnails


  Directory? _cacheDirectory;
  Directory? _thumbnailDirectory;
  Map<String, CacheMetadata> _cacheMetadata = {};
  bool _isInitialized = false;

  /// تهيئة خدمة الـ cache
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/$_cacheDirectoryName');
      _thumbnailDirectory = Directory('${appDir.path}/$_thumbnailDirectoryName');

      // إنشاء المجلدات إذا لم تكن موجودة
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }
      if (!await _thumbnailDirectory!.exists()) {
        await _thumbnailDirectory!.create(recursive: true);
      }

      // تحميل metadata
      await _loadCacheMetadata();

      // تنظيف الـ cache القديم
      await _cleanupOldCache();

      _isInitialized = true;
      debugPrint('AttachmentCacheService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AttachmentCacheService: $e');
    }
  }

  /// الحصول على ملف مخزن مؤقتاً
  Future<File?> getCachedFile(AttachmentModel attachment) async {
    await _ensureInitialized();

    final cacheKey = _generateCacheKey(attachment.url);
    final cachedFile = File('${_cacheDirectory!.path}/$cacheKey');

    if (await cachedFile.exists()) {
      // تحديث وقت الوصول
      _updateAccessTime(cacheKey);
      return cachedFile;
    }

    return null;
  }

  /// الحصول على thumbnail مخزن مؤقتاً
  Future<File?> getCachedThumbnail(AttachmentModel attachment) async {
    await _ensureInitialized();

    if (!attachment.isImage) return null;

    final cacheKey = _generateCacheKey(attachment.url);
    final thumbnailFile = File('${_thumbnailDirectory!.path}/${cacheKey}_thumb');

    if (await thumbnailFile.exists()) {
      _updateAccessTime('${cacheKey}_thumb');
      return thumbnailFile;
    }

    return null;
  }

  /// تحميل وحفظ ملف في الـ cache
  Future<File?> cacheFile(
    AttachmentModel attachment, {
    bool generateThumbnail = true,
    Function(double)? onProgress,
  }) async {
    await _ensureInitialized();

    try {
      final cacheKey = _generateCacheKey(attachment.url);
      final cachedFile = File('${_cacheDirectory!.path}/$cacheKey');

      // تحقق من وجود الملف مسبقاً
      if (await cachedFile.exists()) {
        _updateAccessTime(cacheKey);
        onProgress?.call(1.0);
        return cachedFile;
      }

      // تحقق من صحة الرابط أولاً
      if (attachment.url.isEmpty || !attachment.hasValidUrl) {
        debugPrint('Invalid attachment URL: ${attachment.url}');
        return null;
      }

      // تحميل الملف مع progress tracking
      final client = http.Client();
      final downloadUrl = attachment.directDownloadUrl;

      debugPrint('Attempting to download from: $downloadUrl');

      final request = http.Request('GET', Uri.parse(downloadUrl));
      request.headers.addAll(_getHttpHeaders());

      final response = await client.send(request);

      if (response.statusCode == 200) {
        final contentLength = response.contentLength ?? 0;
        final bytes = <int>[];
        int downloadedBytes = 0;

        await for (final chunk in response.stream) {
          bytes.addAll(chunk);
          downloadedBytes += chunk.length;

          if (contentLength > 0) {
            final progress = downloadedBytes / contentLength;
            onProgress?.call(progress);
          }
        }

        final fileBytes = Uint8List.fromList(bytes);

        // حفظ الملف
        await cachedFile.writeAsBytes(fileBytes);

        // حفظ metadata
        _cacheMetadata[cacheKey] = CacheMetadata(
          url: attachment.url,
          fileName: attachment.fileName,
          fileSize: fileBytes.length,
          mimeType: attachment.mimeType,
          cachedAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          attachmentType: attachment.type,
        );

        // إنشاء thumbnail للصور والفيديوهات
        if (generateThumbnail) {
          if (attachment.isImage) {
            await _generateThumbnail(attachment, fileBytes);
          } else if (attachment.isVideo) {
            await _generateVideoThumbnail(attachment, cachedFile);
          }
        }

        // حفظ metadata
        await _saveCacheMetadata();

        // تنظيف الـ cache إذا تجاوز الحد الأقصى
        await _cleanupIfNeeded();

        debugPrint('File cached successfully: ${attachment.fileName}');
        return cachedFile;
      } else {
        debugPrint('Failed to download file: ${response.statusCode} from $downloadUrl');

        // إذا كان الرابط من Google Drive وفشل، جرب رابط بديل
        if (attachment.url.contains('drive.google.com') && response.statusCode == 404) {
          debugPrint('Trying alternative Google Drive URL...');
          return await _tryAlternativeGoogleDriveUrl(attachment, cacheKey, cachedFile, onProgress);
        }

        return null;
      }
    } catch (e) {
      debugPrint('Error caching file: $e');
      return null;
    }
  }

  /// إنشاء thumbnail للصور
  Future<void> _generateThumbnail(AttachmentModel attachment, Uint8List imageBytes) async {
    try {
      final cacheKey = _generateCacheKey(attachment.url);
      final thumbnailFile = File('${_thumbnailDirectory!.path}/${cacheKey}_thumb');

      // تحسين حجم الصورة المصغرة
      Uint8List thumbnailBytes = imageBytes;

      // إذا كانت الصورة كبيرة، نقوم بضغطها
      if (imageBytes.length > 1024 * 1024) { // أكبر من 1MB
        try {
          // استخدام خدمة تحسين الأصول لضغط الصورة
          final optimizedBytes = await _compressImageForThumbnail(imageBytes);
          if (optimizedBytes != null && optimizedBytes.length < imageBytes.length) {
            thumbnailBytes = optimizedBytes;
          }
        } catch (e) {
          debugPrint('Error compressing thumbnail: $e');
        }
      }

      // التحقق من حجم الصورة المصغرة
      if (thumbnailBytes.length > _maxThumbnailSize ~/ 10) { // لا تتجاوز 10% من الحد الأقصى
        // تقليل الجودة أكثر
        try {
          final furtherCompressed = await _compressImageForThumbnail(thumbnailBytes, quality: _thumbnailQuality ~/ 2);
          if (furtherCompressed != null) {
            thumbnailBytes = furtherCompressed;
          }
        } catch (e) {
          debugPrint('Error further compressing thumbnail: $e');
        }
      }

      await thumbnailFile.writeAsBytes(thumbnailBytes);

      _cacheMetadata['${cacheKey}_thumb'] = CacheMetadata(
        url: attachment.url,
        fileName: '${attachment.fileName}_thumb',
        fileSize: thumbnailBytes.length,
        mimeType: attachment.mimeType,
        cachedAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        attachmentType: attachment.type,
        isThumbnail: true,
      );
    } catch (e) {
      debugPrint('Error generating thumbnail: $e');
    }
  }

  /// ضغط الصورة للصورة المصغرة
  Future<Uint8List?> _compressImageForThumbnail(Uint8List imageBytes, {int quality = 60}) async {
    try {
      // استخدام خدمة تحسين الأصول
      return await AssetOptimizationService.convertToWebP(imageBytes, quality: quality);
    } catch (e) {
      debugPrint('Error compressing image for thumbnail: $e');
      return null;
    }
  }

  /// إنشاء thumbnail للفيديوهات
  Future<void> _generateVideoThumbnail(AttachmentModel attachment, File videoFile) async {
    try {
      final cacheKey = _generateCacheKey(attachment.url);
      final thumbnailFile = File('${_thumbnailDirectory!.path}/${cacheKey}_thumb.jpg');

      // استخدام خدمة استخراج thumbnails من الفيديوهات
      final thumbnailBytes = await VideoThumbnailService.instance
          .generateVideoThumbnail(videoFile.path);

      if (thumbnailBytes != null) {
        await thumbnailFile.writeAsBytes(thumbnailBytes);

        _cacheMetadata['${cacheKey}_thumb'] = CacheMetadata(
          url: attachment.url,
          fileName: '${attachment.fileName}_thumb.jpg',
          fileSize: thumbnailBytes.length,
          mimeType: 'image/jpeg',
          cachedAt: DateTime.now(),
          lastAccessed: DateTime.now(),
          attachmentType: attachment.type,
          isThumbnail: true,
        );

        debugPrint('Video thumbnail generated successfully for: ${attachment.fileName}');
      } else {
        // إذا فشل استخراج thumbnail، استخدم placeholder
        final placeholderBytes = await _createVideoPlaceholderThumbnail();
        if (placeholderBytes != null) {
          await thumbnailFile.writeAsBytes(placeholderBytes);

          _cacheMetadata['${cacheKey}_thumb'] = CacheMetadata(
            url: attachment.url,
            fileName: '${attachment.fileName}_thumb.jpg',
            fileSize: placeholderBytes.length,
            mimeType: 'image/jpeg',
            cachedAt: DateTime.now(),
            lastAccessed: DateTime.now(),
            attachmentType: attachment.type,
            isThumbnail: true,
          );
        }
      }
    } catch (e) {
      debugPrint('Error generating video thumbnail: $e');
    }
  }

  /// إنشاء thumbnail افتراضي للفيديو
  Future<Uint8List?> _createVideoPlaceholderThumbnail() async {
    try {
      // إنشاء صورة بسيطة مع أيقونة فيديو
      // هذا مجرد placeholder - يمكن تحسينه لاحقاً
      return null; // للآن نعيد null
    } catch (e) {
      debugPrint('Error creating video placeholder thumbnail: $e');
      return null;
    }
  }

  /// تنظيف الـ cache القديم
  Future<void> _cleanupOldCache() async {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    for (final entry in _cacheMetadata.entries) {
      final daysDiff = now.difference(entry.value.lastAccessed).inDays;
      if (daysDiff > _maxCacheAge) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      await _removeCachedFile(key);
    }

    if (keysToRemove.isNotEmpty) {
      await _saveCacheMetadata();
      debugPrint('Cleaned up ${keysToRemove.length} old cache files');
    }
  }

  /// تنظيف الـ cache إذا تجاوز الحد الأقصى
  Future<void> _cleanupIfNeeded() async {
    final totalSize = _calculateTotalCacheSize();

    if (totalSize > _maxCacheSize) {
      // ترتيب الملفات حسب آخر وصول (الأقدم أولاً)
      final sortedEntries = _cacheMetadata.entries.toList()
        ..sort((a, b) => a.value.lastAccessed.compareTo(b.value.lastAccessed));

      int removedSize = 0;
      final targetSize = _maxCacheSize * 0.8; // إزالة 20% إضافية

      for (final entry in sortedEntries) {
        if (totalSize - removedSize <= targetSize) break;

        removedSize += entry.value.fileSize;
        await _removeCachedFile(entry.key);
      }

      await _saveCacheMetadata();
      debugPrint('Cache cleanup completed. Removed ${removedSize ~/ 1024} KB');
    }
  }

  /// حساب الحجم الإجمالي للـ cache
  int _calculateTotalCacheSize() {
    return _cacheMetadata.values.fold(0, (sum, metadata) => sum + metadata.fileSize);
  }

  /// إزالة ملف من الـ cache
  Future<void> _removeCachedFile(String cacheKey) async {
    try {
      final file = File('${_cacheDirectory!.path}/$cacheKey');
      if (await file.exists()) {
        await file.delete();
      }

      final thumbnailFile = File('${_thumbnailDirectory!.path}/${cacheKey}_thumb');
      if (await thumbnailFile.exists()) {
        await thumbnailFile.delete();
      }

      _cacheMetadata.remove(cacheKey);
      _cacheMetadata.remove('${cacheKey}_thumb');
    } catch (e) {
      debugPrint('Error removing cached file: $e');
    }
  }

  /// تحديث وقت الوصول
  void _updateAccessTime(String cacheKey) {
    final metadata = _cacheMetadata[cacheKey];
    if (metadata != null) {
      _cacheMetadata[cacheKey] = metadata.copyWith(lastAccessed: DateTime.now());
    }
  }

  /// إنشاء مفتاح cache من URL
  String _generateCacheKey(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// الحصول على HTTP headers
  Map<String, String> _getHttpHeaders() {
    return {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Cache-Control': 'max-age=3600',
    };
  }

  /// تحميل metadata من SharedPreferences
  Future<void> _loadCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataJson = prefs.getString(_metadataKey);

      if (metadataJson != null) {
        // تحويل JSON إلى Map
        // يتطلب تنفيذ serialization/deserialization
        // للآن نبدأ بـ metadata فارغ
        _cacheMetadata = {};
      }
    } catch (e) {
      debugPrint('Error loading cache metadata: $e');
      _cacheMetadata = {};
    }
  }

  /// حفظ metadata في SharedPreferences
  Future<void> _saveCacheMetadata() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // تحويل Map إلى JSON
      // يتطلب تنفيذ serialization/deserialization
      await prefs.setString(_metadataKey, '{}');
    } catch (e) {
      debugPrint('Error saving cache metadata: $e');
    }
  }

  /// التأكد من التهيئة
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// مسح جميع الـ cache
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    try {
      if (await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create();
      }

      if (await _thumbnailDirectory!.exists()) {
        await _thumbnailDirectory!.delete(recursive: true);
        await _thumbnailDirectory!.create();
      }

      _cacheMetadata.clear();
      await _saveCacheMetadata();

      debugPrint('All cache cleared successfully');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  /// الحصول على معلومات الـ cache
  Future<CacheInfo> getCacheInfo() async {
    await _ensureInitialized();

    final totalSize = _calculateTotalCacheSize();
    final fileCount = _cacheMetadata.length;

    return CacheInfo(
      totalSize: totalSize,
      fileCount: fileCount,
      maxSize: _maxCacheSize,
      usagePercentage: (totalSize / _maxCacheSize * 100).clamp(0, 100),
    );
  }

  /// محاولة تحميل من رابط Google Drive بديل
  Future<File?> _tryAlternativeGoogleDriveUrl(
    AttachmentModel attachment,
    String cacheKey,
    File cachedFile,
    Function(double)? onProgress,
  ) async {
    try {
      // استخراج file ID من الرابط الأصلي
      String? fileId;
      final patterns = [
        RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
        RegExp(r'id=([a-zA-Z0-9-_]+)'),
        RegExp(r'/([a-zA-Z0-9-_]+)/view'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(attachment.url);
        if (match != null && match.groupCount > 0) {
          fileId = match.group(1);
          break;
        }
      }

      if (fileId == null) {
        debugPrint('Could not extract file ID from Google Drive URL');
        return null;
      }

      // جرب روابط بديلة
      final alternativeUrls = [
        'https://drive.google.com/uc?export=download&id=$fileId',
        'https://drive.google.com/file/d/$fileId/view?usp=sharing',
        'https://drive.google.com/thumbnail?id=$fileId&sz=w1000',
      ];

      for (final altUrl in alternativeUrls) {
        try {
          debugPrint('Trying alternative URL: $altUrl');

          final client = http.Client();
          final request = http.Request('GET', Uri.parse(altUrl));
          request.headers.addAll(_getHttpHeaders());

          final response = await client.send(request);

          if (response.statusCode == 200) {
            final contentLength = response.contentLength ?? 0;
            final bytes = <int>[];
            int downloadedBytes = 0;

            await for (final chunk in response.stream) {
              bytes.addAll(chunk);
              downloadedBytes += chunk.length;

              if (contentLength > 0) {
                final progress = 0.3 + (downloadedBytes / contentLength) * 0.5;
                onProgress?.call(progress.clamp(0.0, 0.8));
              }
            }

            final fileBytes = Uint8List.fromList(bytes);
            await cachedFile.writeAsBytes(fileBytes);

            debugPrint('Successfully downloaded from alternative URL: $altUrl');
            return cachedFile;
          }
        } catch (e) {
          debugPrint('Failed alternative URL $altUrl: $e');
          continue;
        }
      }

      debugPrint('All alternative URLs failed');
      return null;
    } catch (e) {
      debugPrint('Error trying alternative Google Drive URLs: $e');
      return null;
    }
  }
}

/// معلومات metadata للملفات المخزنة مؤقتاً
class CacheMetadata {
  final String url;
  final String fileName;
  final int fileSize;
  final String mimeType;
  final DateTime cachedAt;
  final DateTime lastAccessed;
  final AttachmentType attachmentType;
  final bool isThumbnail;

  CacheMetadata({
    required this.url,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
    required this.cachedAt,
    required this.lastAccessed,
    required this.attachmentType,
    this.isThumbnail = false,
  });

  CacheMetadata copyWith({
    String? url,
    String? fileName,
    int? fileSize,
    String? mimeType,
    DateTime? cachedAt,
    DateTime? lastAccessed,
    AttachmentType? attachmentType,
    bool? isThumbnail,
  }) {
    return CacheMetadata(
      url: url ?? this.url,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      cachedAt: cachedAt ?? this.cachedAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      attachmentType: attachmentType ?? this.attachmentType,
      isThumbnail: isThumbnail ?? this.isThumbnail,
    );
  }
}

/// معلومات الـ cache
class CacheInfo {
  final int totalSize;
  final int fileCount;
  final int maxSize;
  final double usagePercentage;

  CacheInfo({
    required this.totalSize,
    required this.fileCount,
    required this.maxSize,
    required this.usagePercentage,
  });

  String get formattedTotalSize {
    if (totalSize < 1024) {
      return '$totalSize B';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  String get formattedMaxSize {
    return '${(maxSize / (1024 * 1024)).toStringAsFixed(0)} MB';
  }
}
