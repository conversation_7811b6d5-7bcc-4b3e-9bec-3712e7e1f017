import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/device_error_model.dart';
import '../../models/attachment_model.dart';
import '../../providers/error_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/attachment_picker_widget.dart';
import '../../widgets/attachment_grid_widget.dart';
import '../../widgets/multi_select_searchable_field.dart';
import '../../mixins/unsaved_changes_mixin.dart';

class ErrorEditScreen extends StatefulWidget {
  final DeviceError? errorToEdit;
  final String? selectedCategoryId;

  const ErrorEditScreen({
    super.key,
    this.errorToEdit,
    this.selectedCategoryId,
  });

  @override
  State<ErrorEditScreen> createState() => _ErrorEditScreenState();
}

class _ErrorEditScreenState extends State<ErrorEditScreen> with UnsavedChangesMixin {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Controllers
  late TextEditingController _errorCodeController;
  late TextEditingController _descriptionController;
  late TextEditingController _solutionController;
  late TextEditingController _manufacturerController;
  late TextEditingController _modelController;

  // State variables
  String? _selectedCategoryId;
  String? _selectedManufacturer; // Keep for backward compatibility
  List<String> _selectedManufacturers = []; // Support multiple manufacturers
  List<String> _selectedModels = [];
  bool _isAddingNewModel = false;
  List<AttachmentModel> _attachments = [];
  List<String> _imageUrls = [];
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _errorCodeController = TextEditingController();
    _descriptionController = TextEditingController();
    _solutionController = TextEditingController();
    _manufacturerController = TextEditingController();
    _modelController = TextEditingController();

    // Load existing data if editing
    if (widget.errorToEdit != null) {
      final error = widget.errorToEdit!;
      _errorCodeController.text = error.errorCode;
      _descriptionController.text = error.description;
      _solutionController.text = error.solution;
      _selectedCategoryId = error.categoryId;
      _selectedManufacturer = error.manufacturer;
      _selectedManufacturers = List.from(error.allManufacturers);
      _selectedModels = List.from(error.allModels);
      _attachments = List.from(error.attachments);
      _imageUrls = List.from(error.imageUrls);
    } else if (widget.selectedCategoryId != null) {
      _selectedCategoryId = widget.selectedCategoryId;
    }

    // Add change listeners to detect unsaved changes
    addChangeListeners([
      _errorCodeController,
      _descriptionController,
      _solutionController,
      _manufacturerController,
      _modelController,
    ]);

    // Load categories, manufacturers and models
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

      categoryProvider.fetchCategories();
      manufacturerProvider.fetchManufacturers();

      // Load models for all selected manufacturers
      if (_selectedManufacturer != null) {
        manufacturerProvider.fetchModelsForManufacturer(_selectedManufacturer!);
      }
      for (final manufacturer in _selectedManufacturers) {
        manufacturerProvider.fetchModelsForManufacturer(manufacturer);
      }
    });
  }

  @override
  void dispose() {
    // Remove change listeners
    removeChangeListeners([
      _errorCodeController,
      _descriptionController,
      _solutionController,
      _manufacturerController,
      _modelController,
    ]);

    _errorCodeController.dispose();
    _descriptionController.dispose();
    _solutionController.dispose();
    _manufacturerController.dispose();
    _modelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context);
    final isRTL = localeProvider.isRTL;

    return buildWithUnsavedChangesProtection(
      child: Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        title: Text(
          widget.errorToEdit != null
              ? (isRTL ? 'تعديل العطل' : 'Edit Error')
              : (isRTL ? 'إضافة عطل جديد' : 'Add New Error'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        actions: [
          // Save button in app bar
          TextButton.icon(
            onPressed: (isOperationInProgress || _isSaving) ? null : _saveError,
            icon: _isSaving
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Icon(Icons.save, color: Colors.white),
            label: Text(
              widget.errorToEdit != null
                  ? (isRTL ? 'تحديث' : 'Update')
                  : (isRTL ? 'حفظ' : 'Save'),
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Upload warning
              if (isOperationInProgress)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange[200]!),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.orange[600],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          isRTL
                              ? 'جاري رفع المرفقات... يرجى الانتظار قبل الحفظ'
                              : 'Uploading attachments... Please wait before saving',
                          style: TextStyle(
                            color: Colors.orange[800],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // Error code field
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextFormField(
                    controller: _errorCodeController,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'كود الخطأ' : 'Error Code',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.code),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isRTL ? 'الرجاء إدخال كود الخطأ' : 'Please enter error code';
                      }
                      return null;
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Category field
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: DropdownButtonFormField<String>(
                    isExpanded: true,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'الفئة' : 'Category',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.category),
                    ),
                    value: _selectedCategoryId,
                    items: categoryProvider.categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category.id,
                        child: Text(category.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value;
                      });
                      markAsChanged();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isRTL ? 'الرجاء اختيار الفئة' : 'Please select a category';
                      }
                      return null;
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Manufacturer and Model section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isRTL ? 'معلومات الجهاز' : 'Device Information',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Manufacturer field
                      _buildManufacturerField(manufacturerProvider, isRTL),
                      const SizedBox(height: 16),

                      // Model field
                      _buildModelField(manufacturerProvider, isRTL),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Description field
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextFormField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'وصف العطل' : 'Error Description',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.description),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isRTL ? 'الرجاء إدخال وصف العطل' : 'Please enter error description';
                      }
                      return null;
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Solution field
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextFormField(
                    controller: _solutionController,
                    decoration: InputDecoration(
                      labelText: isRTL ? 'الحل' : 'Solution',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.build),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isRTL ? 'الرجاء إدخال الحل' : 'Please enter solution';
                      }
                      return null;
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Attachments section
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withAlpha(150),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.black.withAlpha(120)
                          : Colors.grey.withAlpha(80),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 0,
                  margin: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with icon and counter
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withAlpha(30),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Icon(
                                Icons.attach_file,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    isRTL ? 'الملفات المرفقة' : 'Attachments',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    isRTL
                                        ? 'صور، فيديوهات، ومستندات'
                                        : 'Images, videos, and documents',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Counter badge
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: _attachments.isEmpty
                                    ? Colors.grey.shade200
                                    : Theme.of(context).colorScheme.primary.withAlpha(30),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: _attachments.isEmpty
                                      ? Colors.grey.shade400
                                      : Theme.of(context).colorScheme.primary,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '${_attachments.length}/10',
                                style: TextStyle(
                                  color: _attachments.isEmpty
                                      ? Colors.grey.shade600
                                      : Theme.of(context).colorScheme.primary,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Current attachments display
                        if (_attachments.isNotEmpty) ...[
                          Text(
                            isRTL ? 'المرفقات الحالية:' : 'Current Attachments:',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),
                          AttachmentGridWidget(
                            attachments: _attachments,
                            showActions: true,
                            onDelete: (attachment) {
                              setState(() {
                                _attachments.remove(attachment);
                              });
                              markAsChanged();
                            },
                            onDownload: (attachment) {
                              // Handle download if needed
                            },
                          ),
                          const SizedBox(height: 16),
                          Divider(color: Colors.grey.shade300),
                          const SizedBox(height: 16),
                        ],

                        // Attachment picker widget
                        AttachmentPickerWidget(
                          initialAttachments: const [], // Start with empty for new additions
                          onAttachmentsChanged: (newAttachments) {
                            setState(() {
                              // Add new attachments to existing ones
                              _attachments.addAll(newAttachments);
                            });
                            markAsChanged();
                          },
                          onUploadStateChanged: (isUploading) {
                            setOperationInProgress(isUploading);
                          },
                          maxAttachments: 10 - _attachments.length, // Adjust max based on current count
                          allowImages: true,
                          allowVideos: true,
                          allowDocuments: true,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildManufacturerField(ManufacturerProvider manufacturerProvider, bool isRTL) {
    return MultiSelectSearchableField(
      label: isRTL ? 'الشركات المصنعة' : 'Manufacturers',
      hint: isRTL ? 'ابحث واختر الشركات المصنعة' : 'Search and select manufacturers',
      options: manufacturerProvider.manufacturers,
      selectedValues: _selectedManufacturers,
      onChanged: (selectedManufacturers) {
        setState(() {
          _selectedManufacturers = selectedManufacturers;
          // Don't clear models when manufacturers change - preserve them
          // Load models for all selected manufacturers
          for (final manufacturer in selectedManufacturers) {
            manufacturerProvider.fetchModelsForManufacturer(manufacturer);
          }
        });
        markAsChanged();
      },
      prefixIcon: Icons.business,
      allowAddNew: true,
      onAddNew: (newManufacturer) async {
        await manufacturerProvider.addManufacturer(newManufacturer);
      },
      addNewLabel: isRTL ? 'إضافة شركة مصنعة جديدة' : 'Add New Manufacturer',
      maxSelections: 5,
      validator: (values) {
        if (values == null || values.isEmpty) {
          return isRTL
              ? 'الرجاء اختيار شركة مصنعة واحدة على الأقل'
              : 'Please select at least one manufacturer';
        }
        return null;
      },
    );
  }

  Widget _buildModelField(ManufacturerProvider manufacturerProvider, bool isRTL) {
    if (_isAddingNewModel) {
      return Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: _modelController,
              decoration: InputDecoration(
                labelText: isRTL ? 'الموديل الجديد' : 'New Model',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.devices),
              ),
              onFieldSubmitted: (value) {
                if (value.isNotEmpty && !_selectedModels.contains(value)) {
                  setState(() {
                    _selectedModels.add(value);
                    _modelController.clear();
                    _isAddingNewModel = false;
                  });
                  markAsChanged();
                }
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              final value = _modelController.text.trim();
              if (value.isNotEmpty && !_selectedModels.contains(value)) {
                setState(() {
                  _selectedModels.add(value);
                  _modelController.clear();
                  _isAddingNewModel = false;
                });
                markAsChanged();
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              setState(() {
                _isAddingNewModel = false;
                _modelController.clear();
              });
            },
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected models display
        if (_selectedModels.isNotEmpty) ...[
          Text(
            isRTL ? 'الموديلات المختارة:' : 'Selected Models:',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _selectedModels.map((model) {
              return Chip(
                label: Text(model),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () {
                  setState(() {
                    _selectedModels.remove(model);
                  });
                  markAsChanged();
                },
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                labelStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // Available models dropdown (if manufacturers are selected)
        if (_selectedManufacturers.isNotEmpty) ...[
          DropdownButtonFormField<String>(
            isExpanded: true,
            decoration: InputDecoration(
              labelText: isRTL ? 'إضافة موديل' : 'Add Model',
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.devices),
              helperText: isRTL ? 'اختياري - يمكن اختيار عدة موديلات' : 'Optional - Multiple models can be selected',
            ),
            value: null, // Always null to allow multiple selections
            items: _getAllAvailableModels(manufacturerProvider)
                .where((model) => !_selectedModels.contains(model))
                .map((model) {
              return DropdownMenuItem<String>(
                value: model,
                child: Text(model),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null && !_selectedModels.contains(value)) {
                setState(() {
                  _selectedModels.add(value);
                });
                markAsChanged();
              }
            },
          ),
          const SizedBox(height: 8),
        ],

        // Add new model button
        TextButton.icon(
          icon: const Icon(Icons.add),
          label: Text(
            isRTL ? 'إضافة موديل جديد' : 'Add New Model',
          ),
          onPressed: () {
            setState(() {
              _isAddingNewModel = true;
            });
          },
        ),
      ],
    );
  }

  /// Get all available models from all selected manufacturers
  List<String> _getAllAvailableModels(ManufacturerProvider manufacturerProvider) {
    final allModels = <String>[];
    for (final manufacturer in _selectedManufacturers) {
      allModels.addAll(manufacturerProvider.getModelsForManufacturer(manufacturer));
    }
    return allModels.toSet().toList(); // Remove duplicates
  }

  Future<void> _saveError() async {
    if (!_formKey.currentState!.validate() || _selectedCategoryId == null) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
      final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
      final currentUser = authProvider.user;

      // Handle manufacturers - ensure all selected manufacturers exist
      for (final manufacturer in _selectedManufacturers) {
        await manufacturerProvider.addManufacturer(manufacturer);
      }

      // Handle models - add any new models to all selected manufacturers
      for (final model in _selectedModels) {
        for (final manufacturer in _selectedManufacturers) {
          await manufacturerProvider.addModel(manufacturer, model);
        }
      }

      // For backward compatibility, use the first manufacturer and model
      String mainManufacturer = _selectedManufacturers.isNotEmpty ? _selectedManufacturers.first : '';
      String mainModel = _selectedModels.isNotEmpty ? _selectedModels.first : '';

      bool success;

      if (widget.errorToEdit != null) {
        // Update existing error
        final updatedError = widget.errorToEdit!.copyWith(
          categoryId: _selectedCategoryId!,
          manufacturer: mainManufacturer,
          manufacturers: _selectedManufacturers,
          model: mainModel,
          models: _selectedModels,
          errorCode: _errorCodeController.text.trim(),
          description: _descriptionController.text.trim(),
          solution: _solutionController.text.trim(),
          imageUrls: _imageUrls,
          attachments: _attachments,
          updatedAt: DateTime.now(),
          updatedBy: currentUser?.id ?? 'unknown',
        );

        success = await errorProvider.updateError(updatedError, widget.errorToEdit!.categoryId);
      } else {
        // Create new error
        final newError = DeviceError(
          id: '', // Will be generated by Firestore
          categoryId: _selectedCategoryId!,
          manufacturer: mainManufacturer,
          manufacturers: _selectedManufacturers,
          model: mainModel,
          models: _selectedModels,
          errorCode: _errorCodeController.text.trim(),
          description: _descriptionController.text.trim(),
          solution: _solutionController.text.trim(),
          imageUrls: _imageUrls,
          attachments: _attachments,
          createdAt: DateTime.now(),
          createdBy: currentUser?.id ?? 'unknown',
          updatedAt: DateTime.now(),
          updatedBy: currentUser?.id ?? 'unknown',
        );

        success = await errorProvider.addError(newError);
      }

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.errorToEdit != null
                    ? (localeProvider.isRTL ? 'تم تحديث العطل بنجاح' : 'Error updated successfully')
                    : (localeProvider.isRTL ? 'تمت إضافة العطل بنجاح' : 'Error added successfully'),
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.errorToEdit != null
                    ? (localeProvider.isRTL ? 'فشل تحديث العطل' : 'Failed to update error')
                    : (localeProvider.isRTL ? 'فشل إضافة العطل' : 'Failed to add error'),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}